[2025-03-18 12:50:40,644] [run.py[line:153]] [INFO] [TransDhTask init]
[2025-03-18 12:50:41,729] [run.py[line:158]] [INFO] [任务:1002 -> audio_url:./temp/example/audio.wav  video_url:./temp/example/video.mp4]
[2025-03-18 12:50:41,732] [run.py[line:158]] [INFO] [[1002] -> ffmpeg video: ffmpeg -loglevel warning -i ./temp/example/video.mp4 -c:v libx264 -crf 15 -an -y ./temp/1002_format.mp4]
[2025-03-18 12:50:41,790] [run.py[line:158]] [ERROR] [[1002]预处理失败，异常信息:[format video error]]
[2025-03-18 12:50:41,790] [run.py[line:158]] [ERROR] [[1002]任务执行失败，异常信息:[[1002]预处理失败，异常信息:[format video error]]]
[2025-03-18 12:50:41,791] [run.py[line:158]] [INFO] [>>> 任务:1002 耗时:0.06167912483215332 ]
[2025-03-18 12:50:57,817] [run.py[line:143]] [INFO] [TransDhTask init]
[2025-03-18 12:50:58,906] [run.py[line:147]] [INFO] [任务:1002 -> audio_url:./temp/example/audio.wav  video_url:./temp/example/video.mp4]
[2025-03-18 12:50:58,908] [run.py[line:147]] [INFO] [[1002] -> ffmpeg video: ffmpeg -loglevel warning -i ./temp/example/video.mp4 -c:v libx264 -crf 15 -an -y ./temp/1002_format.mp4]
[2025-03-18 12:50:58,964] [run.py[line:147]] [ERROR] [[1002]预处理失败，异常信息:[format video error]]
[2025-03-18 12:50:58,965] [run.py[line:147]] [ERROR] [[1002]任务执行失败，异常信息:[[1002]预处理失败，异常信息:[format video error]]]
[2025-03-18 12:50:58,966] [run.py[line:147]] [INFO] [>>> 任务:1002 耗时:0.059505462646484375 ]
[2025-03-18 12:52:06,385] [run.py[line:143]] [INFO] [TransDhTask init]
[2025-03-18 12:52:07,560] [run.py[line:147]] [INFO] [任务:1002 -> audio_url:./example/audio.wav  video_url:./example/video.mp4]
[2025-03-18 12:52:07,646] [run.py[line:147]] [INFO] [[1002] -> ffmpeg video: ffmpeg -loglevel warning -i ./example/video.mp4 -crf 15 -vcodec copy -an -y ./1002_format.mp4]
[2025-03-18 12:52:07,801] [run.py[line:147]] [INFO] [[1002] -> ffmpeg audio: ffmpeg -loglevel warning -i ./example/audio.wav -ac 1 -ar 16000 -acodec pcm_s16le -y  ./1002_format.wav]
[2025-03-18 12:52:07,922] [run.py[line:147]] [INFO] [[1002] -> 预处理耗时:0.35927414894104004s]
[2025-03-18 12:52:10,169] [run.py[line:147]] [INFO] [[1002] -> get_aud_feat1 cost:2.245649576187134s]
[2025-03-18 12:52:11,702] [process.py[line:108]] [INFO] [>>> init_wh_process进程启动]
[2025-03-18 12:52:20,087] [process.py[line:108]] [INFO] [[1002]init_wh result :[0.8809176216714891]， cost: 8.382684469223022 s]
[2025-03-18 12:52:20,090] [run.py[line:147]] [INFO] [[1002] -> wh: [0.8809176216714891]]
[2025-03-18 12:52:21,453] [process.py[line:108]] [INFO] [>>> 数字人图片处理进程启动]
[2025-03-18 12:52:24,015] [process.py[line:108]] [INFO] [[1002]任务视频驱动队列启动 batch_size:4, len:150]
[2025-03-18 12:52:24,050] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 开始循环]
[2025-03-18 12:52:24,085] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:4]
[2025-03-18 12:52:24,112] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:8]
[2025-03-18 12:52:24,122] [process.py[line:108]] [INFO] [>>> audio_transfer get message:4]
[2025-03-18 12:52:24,139] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:12]
[2025-03-18 12:52:24,148] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:16]
[2025-03-18 12:52:24,161] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:20]
[2025-03-18 12:52:24,173] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:24]
[2025-03-18 12:52:24,185] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:28]
[2025-03-18 12:52:24,197] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:32]
[2025-03-18 12:52:24,208] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:36]
[2025-03-18 12:52:24,222] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:40]
[2025-03-18 12:52:24,232] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:44]
[2025-03-18 12:52:25,722] [process.py[line:108]] [INFO] [[1002] -> frame_id:[4] 模糊置信度:[0.969]]
[2025-03-18 12:52:25,723] [process.py[line:108]] [INFO] [[1002] -> need chaofen .]
[2025-03-18 12:52:25,905] [utils.py[line:145]] [INFO] [Note: detected 72 virtual cores but NumExpr set to maximum of 64, check "NUMEXPR_MAX_THREADS" environment variable.]
[2025-03-18 12:52:25,906] [utils.py[line:148]] [INFO] [Note: NumExpr detected 72 cores but "NUMEXPR_MAX_THREADS" not set, so enforcing safe limit of 8.]
[2025-03-18 12:52:25,907] [utils.py[line:160]] [INFO] [NumExpr defaulting to 8 threads.]
[2025-03-18 12:52:26,083] [process.py[line:108]] [INFO] [[4] -> chaofen  cost:1.9595112800598145s]
[2025-03-18 12:52:31,071] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:4, cost:6.948575258255005s]
[2025-03-18 12:52:31,116] [process.py[line:108]] [INFO] [>>> audio_transfer get message:8]
[2025-03-18 12:52:31,126] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:48]
[2025-03-18 12:52:31,347] [process.py[line:108]] [INFO] [[8] -> chaofen  cost:0.2294461727142334s]
[2025-03-18 12:52:31,576] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:8, cost:0.45979762077331543s]
[2025-03-18 12:52:31,605] [process.py[line:108]] [INFO] [>>> audio_transfer get message:12]
[2025-03-18 12:52:31,615] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:52]
[2025-03-18 12:52:31,818] [process.py[line:108]] [INFO] [[12] -> chaofen  cost:0.21271824836730957s]
[2025-03-18 12:52:32,036] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:12, cost:0.43187427520751953s]
[2025-03-18 12:52:32,060] [process.py[line:108]] [INFO] [>>> audio_transfer get message:16]
[2025-03-18 12:52:32,072] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:56]
[2025-03-18 12:52:32,279] [process.py[line:108]] [INFO] [[16] -> chaofen  cost:0.21899199485778809s]
[2025-03-18 12:52:32,530] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:16, cost:0.47049522399902344s]
[2025-03-18 12:52:32,552] [process.py[line:108]] [INFO] [>>> audio_transfer get message:20]
[2025-03-18 12:52:32,567] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:60]
[2025-03-18 12:52:32,766] [process.py[line:108]] [INFO] [[20] -> chaofen  cost:0.21334147453308105s]
[2025-03-18 12:52:32,993] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:20, cost:0.4411466121673584s]
[2025-03-18 12:52:33,015] [process.py[line:108]] [INFO] [>>> audio_transfer get message:24]
[2025-03-18 12:52:33,028] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:64]
[2025-03-18 12:52:33,229] [process.py[line:108]] [INFO] [[24] -> chaofen  cost:0.21344351768493652s]
[2025-03-18 12:52:33,457] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:24, cost:0.44205546379089355s]
[2025-03-18 12:52:33,479] [process.py[line:108]] [INFO] [>>> audio_transfer get message:28]
[2025-03-18 12:52:33,493] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:68]
[2025-03-18 12:52:33,697] [process.py[line:108]] [INFO] [[28] -> chaofen  cost:0.21679949760437012s]
[2025-03-18 12:52:33,924] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:28, cost:0.4448537826538086s]
[2025-03-18 12:52:33,946] [process.py[line:108]] [INFO] [>>> audio_transfer get message:32]
[2025-03-18 12:52:33,960] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:72]
[2025-03-18 12:52:34,159] [process.py[line:108]] [INFO] [[32] -> chaofen  cost:0.21156740188598633s]
[2025-03-18 12:52:34,381] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:32, cost:0.43474769592285156s]
[2025-03-18 12:52:34,403] [process.py[line:108]] [INFO] [>>> audio_transfer get message:36]
[2025-03-18 12:52:34,417] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:76]
[2025-03-18 12:52:34,618] [process.py[line:108]] [INFO] [[36] -> chaofen  cost:0.21408891677856445s]
[2025-03-18 12:52:34,844] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:36, cost:0.4406392574310303s]
[2025-03-18 12:52:34,867] [process.py[line:108]] [INFO] [>>> audio_transfer get message:40]
[2025-03-18 12:52:34,881] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:80]
[2025-03-18 12:52:35,099] [process.py[line:108]] [INFO] [[40] -> chaofen  cost:0.23105645179748535s]
[2025-03-18 12:52:35,328] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:40, cost:0.46161866188049316s]
[2025-03-18 12:52:35,350] [process.py[line:108]] [INFO] [>>> audio_transfer get message:44]
[2025-03-18 12:52:35,363] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:84]
[2025-03-18 12:52:35,577] [process.py[line:108]] [INFO] [[44] -> chaofen  cost:0.22576594352722168s]
[2025-03-18 12:52:35,808] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:44, cost:0.4577639102935791s]
[2025-03-18 12:52:35,832] [process.py[line:108]] [INFO] [>>> audio_transfer get message:48]
[2025-03-18 12:52:35,846] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:88]
[2025-03-18 12:52:36,047] [process.py[line:108]] [INFO] [[48] -> chaofen  cost:0.21441864967346191s]
[2025-03-18 12:52:36,278] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:48, cost:0.4459846019744873s]
[2025-03-18 12:52:36,301] [process.py[line:108]] [INFO] [>>> audio_transfer get message:52]
[2025-03-18 12:52:36,315] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:92]
[2025-03-18 12:52:36,521] [process.py[line:108]] [INFO] [[52] -> chaofen  cost:0.2181704044342041s]
[2025-03-18 12:52:36,777] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:52, cost:0.47586750984191895s]
[2025-03-18 12:52:36,798] [process.py[line:108]] [INFO] [>>> audio_transfer get message:56]
[2025-03-18 12:52:36,817] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:96]
[2025-03-18 12:52:37,014] [process.py[line:108]] [INFO] [[56] -> chaofen  cost:0.2147221565246582s]
[2025-03-18 12:52:37,247] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:56, cost:0.4486660957336426s]
[2025-03-18 12:52:37,266] [process.py[line:108]] [INFO] [>>> audio_transfer get message:60]
[2025-03-18 12:52:37,281] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:100]
[2025-03-18 12:52:37,483] [process.py[line:108]] [INFO] [[60] -> chaofen  cost:0.21598410606384277s]
[2025-03-18 12:52:37,703] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:60, cost:0.43683695793151855s]
[2025-03-18 12:52:37,722] [process.py[line:108]] [INFO] [>>> audio_transfer get message:64]
[2025-03-18 12:52:37,736] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:104]
[2025-03-18 12:52:37,941] [process.py[line:108]] [INFO] [[64] -> chaofen  cost:0.2180624008178711s]
[2025-03-18 12:52:38,163] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:64, cost:0.4412345886230469s]
[2025-03-18 12:52:38,183] [process.py[line:108]] [INFO] [>>> audio_transfer get message:68]
[2025-03-18 12:52:38,197] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:108]
[2025-03-18 12:52:38,397] [process.py[line:108]] [INFO] [[68] -> chaofen  cost:0.21321654319763184s]
[2025-03-18 12:52:38,637] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:68, cost:0.45404863357543945s]
[2025-03-18 12:52:38,656] [process.py[line:108]] [INFO] [>>> audio_transfer get message:72]
[2025-03-18 12:52:38,670] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:112]
[2025-03-18 12:52:38,877] [process.py[line:108]] [INFO] [[72] -> chaofen  cost:0.21999263763427734s]
[2025-03-18 12:52:39,100] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:72, cost:0.4440436363220215s]
[2025-03-18 12:52:39,119] [process.py[line:108]] [INFO] [>>> audio_transfer get message:76]
[2025-03-18 12:52:39,133] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:116]
[2025-03-18 12:52:39,347] [process.py[line:108]] [INFO] [[76] -> chaofen  cost:0.22693967819213867s]
[2025-03-18 12:52:39,568] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:76, cost:0.4492220878601074s]
[2025-03-18 12:52:39,586] [process.py[line:108]] [INFO] [>>> audio_transfer get message:80]
[2025-03-18 12:52:39,601] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:120]
[2025-03-18 12:52:39,801] [process.py[line:108]] [INFO] [[80] -> chaofen  cost:0.21407222747802734s]
[2025-03-18 12:52:40,024] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:80, cost:0.4377562999725342s]
[2025-03-18 12:52:40,052] [process.py[line:108]] [INFO] [>>> audio_transfer get message:84]
[2025-03-18 12:52:40,068] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:124]
[2025-03-18 12:52:40,270] [process.py[line:108]] [INFO] [[84] -> chaofen  cost:0.21637320518493652s]
[2025-03-18 12:52:40,494] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:84, cost:0.44118523597717285s]
[2025-03-18 12:52:40,513] [process.py[line:108]] [INFO] [>>> audio_transfer get message:88]
[2025-03-18 12:52:40,527] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:128]
[2025-03-18 12:52:40,731] [process.py[line:108]] [INFO] [[88] -> chaofen  cost:0.2170412540435791s]
[2025-03-18 12:52:40,951] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:88, cost:0.4383111000061035s]
[2025-03-18 12:52:40,971] [process.py[line:108]] [INFO] [>>> audio_transfer get message:92]
[2025-03-18 12:52:40,984] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:132]
[2025-03-18 12:52:41,187] [process.py[line:108]] [INFO] [[92] -> chaofen  cost:0.2148122787475586s]
[2025-03-18 12:52:41,416] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:92, cost:0.4454326629638672s]
[2025-03-18 12:52:41,439] [process.py[line:108]] [INFO] [>>> audio_transfer get message:96]
[2025-03-18 12:52:41,451] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:136]
[2025-03-18 12:52:41,663] [process.py[line:108]] [INFO] [[96] -> chaofen  cost:0.222761869430542s]
[2025-03-18 12:52:41,887] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:96, cost:0.4477369785308838s]
[2025-03-18 12:52:41,906] [process.py[line:108]] [INFO] [>>> audio_transfer get message:100]
[2025-03-18 12:52:41,920] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:140]
[2025-03-18 12:52:42,123] [process.py[line:108]] [INFO] [[100] -> chaofen  cost:0.21576929092407227s]
[2025-03-18 12:52:42,359] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:100, cost:0.4525878429412842s]
[2025-03-18 12:52:42,379] [process.py[line:108]] [INFO] [>>> audio_transfer get message:104]
[2025-03-18 12:52:42,394] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:144]
[2025-03-18 12:52:42,596] [process.py[line:108]] [INFO] [[104] -> chaofen  cost:0.21553897857666016s]
[2025-03-18 12:52:42,836] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:104, cost:0.45633435249328613s]
[2025-03-18 12:52:42,855] [process.py[line:108]] [INFO] [>>> audio_transfer get message:108]
[2025-03-18 12:52:42,870] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[4], current_idx:148]
[2025-03-18 12:52:42,873] [process.py[line:108]] [INFO] [append imgs over]
[2025-03-18 12:52:42,879] [process.py[line:108]] [INFO] [drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据结束]
[2025-03-18 12:52:43,073] [process.py[line:108]] [INFO] [[108] -> chaofen  cost:0.21662592887878418s]
[2025-03-18 12:52:43,297] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:108, cost:0.4421381950378418s]
[2025-03-18 12:52:43,318] [process.py[line:108]] [INFO] [>>> audio_transfer get message:112]
[2025-03-18 12:52:43,332] [process.py[line:108]] [INFO] [[1002]任务预处理进程结束]
[2025-03-18 12:52:43,531] [process.py[line:108]] [INFO] [[112] -> chaofen  cost:0.21228814125061035s]
[2025-03-18 12:52:43,791] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:112, cost:0.47336626052856445s]
[2025-03-18 12:52:43,811] [process.py[line:108]] [INFO] [>>> audio_transfer get message:116]
[2025-03-18 12:52:44,034] [process.py[line:108]] [INFO] [[116] -> chaofen  cost:0.2223985195159912s]
[2025-03-18 12:52:44,262] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:116, cost:0.4509873390197754s]
[2025-03-18 12:52:44,281] [process.py[line:108]] [INFO] [>>> audio_transfer get message:120]
[2025-03-18 12:52:44,499] [process.py[line:108]] [INFO] [[120] -> chaofen  cost:0.21637916564941406s]
[2025-03-18 12:52:44,742] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:120, cost:0.46120476722717285s]
[2025-03-18 12:52:44,762] [process.py[line:108]] [INFO] [>>> audio_transfer get message:124]
[2025-03-18 12:52:44,981] [process.py[line:108]] [INFO] [[124] -> chaofen  cost:0.21886157989501953s]
[2025-03-18 12:52:45,240] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:124, cost:0.4781684875488281s]
[2025-03-18 12:52:45,258] [process.py[line:108]] [INFO] [>>> audio_transfer get message:128]
[2025-03-18 12:52:45,474] [process.py[line:108]] [INFO] [[128] -> chaofen  cost:0.21480226516723633s]
[2025-03-18 12:52:45,708] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:128, cost:0.44920992851257324s]
[2025-03-18 12:52:45,726] [process.py[line:108]] [INFO] [>>> audio_transfer get message:132]
[2025-03-18 12:52:45,943] [process.py[line:108]] [INFO] [[132] -> chaofen  cost:0.21567535400390625s]
[2025-03-18 12:52:46,181] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:132, cost:0.45519399642944336s]
[2025-03-18 12:52:46,200] [process.py[line:108]] [INFO] [>>> audio_transfer get message:136]
[2025-03-18 12:52:46,418] [process.py[line:108]] [INFO] [[136] -> chaofen  cost:0.21763992309570312s]
[2025-03-18 12:52:46,662] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:136, cost:0.4619452953338623s]
[2025-03-18 12:52:46,681] [process.py[line:108]] [INFO] [>>> audio_transfer get message:140]
[2025-03-18 12:52:46,900] [process.py[line:108]] [INFO] [[140] -> chaofen  cost:0.21794748306274414s]
[2025-03-18 12:52:47,146] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:140, cost:0.4646177291870117s]
[2025-03-18 12:52:47,166] [process.py[line:108]] [INFO] [>>> audio_transfer get message:144]
[2025-03-18 12:52:47,382] [process.py[line:108]] [INFO] [[144] -> chaofen  cost:0.21491503715515137s]
[2025-03-18 12:52:47,619] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:144, cost:0.4536001682281494s]
[2025-03-18 12:52:47,639] [process.py[line:108]] [INFO] [>>> audio_transfer get message:148]
[2025-03-18 12:52:47,857] [process.py[line:108]] [INFO] [[148] -> chaofen  cost:0.21780657768249512s]
[2025-03-18 12:52:48,098] [process.py[line:108]] [INFO] [audio_transfer >>>>>>>>>>> 发送完成数据大小:4, frameId:148, cost:0.459348201751709s]
[2025-03-18 12:52:48,104] [process.py[line:108]] [INFO] [>>> audio_transfer get exception msg:-1]
[2025-03-18 12:52:48,105] [process.py[line:108]] [INFO] [[1002]任务数字人图片处理已完成]
[2025-03-18 12:52:48,146] [run.py[line:43]] [INFO] [Custom VideoWriter [1002]视频帧队列处理已结束]
[2025-03-18 12:52:48,151] [run.py[line:46]] [INFO] [Custom VideoWriter Silence Video saved in /mnt/nfs/bj4-v100-23/data1/yubosun/git_proj/heygem/heygem_ori_so/1002-t.mp4]
[2025-03-18 12:52:48,155] [run.py[line:118]] [INFO] [Custom command:ffmpeg -loglevel warning -y -i ./example/audio.wav -i ./1002-t.mp4 -c:a aac -c:v libx264 -crf 15 -strict -2 ./1002-r.mp4]
[2025-03-18 12:53:06,908] [run.py[line:147]] [INFO] [>>> 任务:1002 耗时:59.3451771736145 ]
[2025-08-04 21:51:38,239] [run.py[line:185]] [INFO] [TransDhTask init (with Caching and Batching Optimization)]
[2025-08-04 21:52:31,626] [run.py[line:185]] [INFO] [TransDhTask init (with Caching and Batching Optimization)]
[2025-08-04 21:52:32,203] [run.py[line:185]] [INFO] [Created persistent face cache directory at: ./face_cache/]
[2025-08-04 21:52:42,221] [run.py[line:189]] [INFO] [����:1004 -> audio_url:example/audio.wav  video_url:example/video.mp4]
[2025-08-04 21:52:42,333] [run.py[line:189]] [INFO] [[1004] -> ffmpeg video: ffmpeg -loglevel warning -i example/video.mp4 -crf 15 -vcodec copy -an -y ./1004_format.mp4]
[2025-08-04 21:52:42,729] [run.py[line:189]] [INFO] [[1004] -> ffmpeg audio: ffmpeg -loglevel warning -i example/audio.wav -ac 1 -ar 16000 -acodec pcm_s16le -y  ./1004_format.wav]
[2025-08-04 21:52:42,789] [run.py[line:189]] [INFO] [[1004] -> Ԥ�����ʱ:0.5687165260314941s]
[2025-08-04 21:52:42,790] [run.py[line:189]] [INFO] [[1004] Checking for face data cache at: face_cache\1004_format.face_data.pt]
[2025-08-04 21:52:42,790] [run.py[line:189]] [INFO] [[1004] No valid cache found. Starting full video analysis.]
[2025-08-04 21:53:13,873] [run.py[line:189]] [INFO] [[1004] Face data computed and saved to cache. Time: 31.08s]
[2025-08-04 21:53:13,902] [run.py[line:189]] [INFO] [[1004] -> Video analysis/cache load cost: 31.11s]
[2025-08-04 21:53:13,944] [run.py[line:189]] [ERROR] [[1004]����ִ��ʧ�ܣ��쳣��Ϣ:[[1004]��Ƶ������ȡʧ�ܣ��쳣��Ϣ:[Numpy is not available]]]
[2025-08-04 21:53:13,946] [sweep_bot.py[line:28]] [INFO] [ɨ�ػ���������Ŀ¼:[./]]
[2025-08-04 21:53:14,105] [sweep_bot.py[line:39]] [ERROR] [ɨ�ػ����˹����쳣���쳣��Ϣ:[[WinError 32] ��һ����������ʹ�ô��ļ��������޷����ʡ�: './log\\dh.log']]
[2025-08-04 21:53:14,106] [run.py[line:189]] [INFO] [>>> ����:1004 ��ʱ:31.885007858276367 ]
[2025-08-04 22:03:37,572] [run.py[line:185]] [INFO] [TransDhTask init (with Caching and Batching Optimization)]
[2025-08-04 22:03:38,171] [run.py[line:185]] [INFO] [Created persistent face cache directory at: ./face_cache/]
[2025-08-04 22:03:48,189] [run.py[line:189]] [INFO] [����:1004 -> audio_url:example/audio.wav  video_url:example/video.mp4]
[2025-08-04 22:03:48,389] [run.py[line:189]] [INFO] [[1004] -> ffmpeg video: ffmpeg -loglevel warning -i example/video.mp4 -c:v libx264 -crf 15 -an -y ./temp\1004_format.mp4]
[2025-08-04 22:03:48,433] [run.py[line:189]] [ERROR] [[1004]����ִ��ʧ�ܣ��쳣��Ϣ:[[1004]Ԥ����ʧ�ܣ��쳣��Ϣ:[format video error]]]
[2025-08-04 22:03:48,434] [sweep_bot.py[line:23]] [INFO] [ɨ�ػ������޷��ҵ�Ŀ��:[./temp]]
[2025-08-04 22:03:48,434] [sweep_bot.py[line:39]] [ERROR] [ɨ�ػ����˹����쳣���쳣��Ϣ:[[WinError 3] ϵͳ�Ҳ���ָ����·����: './temp']]
[2025-08-04 22:03:48,435] [run.py[line:189]] [INFO] [>>> ����:1004 ��ʱ:0.2445695400238037 ]
[2025-08-04 22:12:51,531] [run.py[line:185]] [INFO] [TransDhTask init (with Caching and Batching Optimization)]
[2025-08-04 22:13:02,045] [run.py[line:189]] [INFO] [����:1004 -> audio_url:example/audio.wav  video_url:example/video.mp4]
[2025-08-04 22:13:02,273] [run.py[line:189]] [INFO] [[1004] -> ffmpeg video: ffmpeg -loglevel warning -i example/video.mp4 -c:v libx264 -crf 15 -an -y ./temp\1004_format.mp4]
[2025-08-04 22:13:02,341] [run.py[line:189]] [ERROR] [[1004]����ִ��ʧ�ܣ��쳣��Ϣ:[[1004]Ԥ����ʧ�ܣ��쳣��Ϣ:[format video error]]]
[2025-08-04 22:13:02,342] [sweep_bot.py[line:23]] [INFO] [ɨ�ػ������޷��ҵ�Ŀ��:[./temp]]
[2025-08-04 22:13:02,342] [sweep_bot.py[line:39]] [ERROR] [ɨ�ػ����˹����쳣���쳣��Ϣ:[[WinError 3] ϵͳ�Ҳ���ָ����·����: './temp']]
[2025-08-04 22:13:02,342] [run.py[line:189]] [INFO] [>>> ����:1004 ��ʱ:0.2968788146972656 ]
[2025-08-04 22:14:27,088] [run.py[line:185]] [INFO] [TransDhTask init (with Caching and Batching Optimization)]
[2025-08-04 22:14:37,577] [run.py[line:189]] [INFO] [����:1004 -> audio_url:example/audio.wav  video_url:example/video.mp4]
[2025-08-04 22:14:37,865] [run.py[line:189]] [INFO] [[1004] -> ffmpeg video: ffmpeg -loglevel warning -i example/video.mp4 -c:v libx264 -crf 15 -an -y ./temp\1004_format.mp4]
[2025-08-04 22:14:37,906] [run.py[line:189]] [ERROR] [[1004]����ִ��ʧ�ܣ��쳣��Ϣ:[[1004]Ԥ����ʧ�ܣ��쳣��Ϣ:[format video error]]]
[2025-08-04 22:14:37,906] [sweep_bot.py[line:28]] [INFO] [ɨ�ػ���������Ŀ¼:[./temp]]
[2025-08-04 22:14:37,907] [run.py[line:189]] [INFO] [>>> ����:1004 ��ʱ:0.3290376663208008 ]
[2025-08-04 22:15:25,635] [run.py[line:185]] [INFO] [TransDhTask init (with Caching and Batching Optimization)]
[2025-08-04 22:15:36,146] [run.py[line:189]] [INFO] [����:1004 -> audio_url:example/audio.wav  video_url:example/video.mp4]
[2025-08-04 22:15:36,318] [run.py[line:189]] [INFO] [[1004] -> ffmpeg video: ffmpeg -loglevel warning -i example/video.mp4 -c:v libx264 -crf 15 -an -y ./temp\1004_format.mp4]
[2025-08-04 22:15:36,360] [run.py[line:189]] [ERROR] [[1004]����ִ��ʧ�ܣ��쳣��Ϣ:[[1004]Ԥ����ʧ�ܣ��쳣��Ϣ:[format video error]]]
[2025-08-04 22:15:36,360] [sweep_bot.py[line:28]] [INFO] [ɨ�ػ���������Ŀ¼:[./temp]]
[2025-08-04 22:15:36,361] [run.py[line:189]] [INFO] [>>> ����:1004 ��ʱ:0.2140798568725586 ]
[2025-08-04 22:17:31,960] [run.py[line:185]] [INFO] [TransDhTask init (with Caching and Batching Optimization)]
[2025-08-04 22:17:42,453] [run.py[line:189]] [INFO] [����:1004 -> audio_url:example/audio.wav  video_url:example/video.mp4]
[2025-08-04 22:17:42,614] [run.py[line:189]] [INFO] [[1004] -> ffmpeg video: ffmpeg -loglevel warning -i example/video.mp4 -c:v libx264 -crf 15 -an -y ./temp\1004_format.mp4]
[2025-08-04 22:17:42,667] [run.py[line:189]] [ERROR] [[1004]����ִ��ʧ�ܣ��쳣��Ϣ:[[1004]Ԥ����ʧ�ܣ��쳣��Ϣ:[format video error]]]
[2025-08-04 22:17:42,667] [sweep_bot.py[line:28]] [INFO] [ɨ�ػ���������Ŀ¼:[./temp]]
[2025-08-04 22:17:42,668] [run.py[line:189]] [INFO] [>>> ����:1004 ��ʱ:0.21433496475219727 ]
[2025-08-04 22:18:20,297] [run.py[line:185]] [INFO] [TransDhTask init (with Caching and Batching Optimization)]
[2025-08-04 22:18:30,815] [run.py[line:189]] [INFO] [����:1004 -> audio_url:example/audio.wav  video_url:example/video.mp4]
[2025-08-04 22:18:30,975] [run.py[line:189]] [INFO] [[1004] -> ffmpeg video: ffmpeg -loglevel warning -i example/video.mp4 -c:v libx264 -crf 15 -an -y ./temp\1004_format.mp4]
[2025-08-04 22:18:31,019] [run.py[line:189]] [ERROR] [[1004]����ִ��ʧ�ܣ��쳣��Ϣ:[[1004]Ԥ����ʧ�ܣ��쳣��Ϣ:[format video error]]]
[2025-08-04 22:18:31,019] [sweep_bot.py[line:28]] [INFO] [ɨ�ػ���������Ŀ¼:[./temp]]
[2025-08-04 22:18:31,020] [run.py[line:189]] [INFO] [>>> ����:1004 ��ʱ:0.20383238792419434 ]
[2025-08-04 22:18:38,414] [process.py[line:108]] [INFO] [>>> Digital human synthesis process started (Refactored for Batching)]
[2025-08-04 22:21:05,965] [run.py[line:185]] [INFO] [TransDhTask init (with Caching and Batching Optimization)]
[2025-08-04 22:21:16,471] [run.py[line:189]] [INFO] [����:1004 -> audio_url:example/audio.wav  video_url:example/video.mp4]
[2025-08-04 22:21:16,498] [run.py[line:189]] [INFO] [[1004] -> ffmpeg video: ffmpeg -loglevel warning -i example/video.mp4 -crf 15 -vcodec copy -an -y ./temp\1004_format.mp4]
[2025-08-04 22:21:16,580] [run.py[line:189]] [INFO] [[1004] -> ffmpeg audio: ffmpeg -loglevel warning -i example/audio.wav -ac 1 -ar 16000 -acodec pcm_s16le -y  ./temp\1004_format.wav]
[2025-08-04 22:21:16,632] [run.py[line:189]] [INFO] [[1004] -> Ԥ�����ʱ:0.16076350212097168s]
[2025-08-04 22:21:16,632] [run.py[line:189]] [INFO] [[1004] Checking for face data cache at: face_cache\1004_format.face_data.pt]
[2025-08-04 22:21:16,633] [run.py[line:189]] [INFO] [[1004] No valid cache found. Starting full video analysis.]
[2025-08-04 22:21:26,805] [process.py[line:108]] [INFO] [>>> Digital human synthesis process started (Refactored for Batching)]
[2025-08-04 22:23:22,147] [run.py[line:189]] [ERROR] [[1004]����ִ��ʧ�ܣ��쳣��Ϣ:[[1004] ��Ƶ����ʧ��: ]]
[2025-08-04 22:23:24,584] [sweep_bot.py[line:28]] [INFO] [ɨ�ػ���������Ŀ¼:[./temp]]
[2025-08-04 22:23:24,595] [run.py[line:189]] [INFO] [>>> ����:1004 ��ʱ:128.1239790916443 ]
[2025-08-04 22:27:11,114] [run.py[line:185]] [INFO] [TransDhTask init (with Caching and Batching Optimization)]
[2025-08-04 22:27:21,707] [run.py[line:189]] [INFO] [����:1004 -> audio_url:example/audio.wav  video_url:example/video.mp4]
[2025-08-04 22:27:21,770] [run.py[line:189]] [INFO] [[1004] -> ffmpeg video: ffmpeg -loglevel warning -i example/video.mp4 -crf 15 -vcodec copy -an -y ./temp\1004_format.mp4]
[2025-08-04 22:27:21,829] [run.py[line:189]] [INFO] [[1004] -> ffmpeg audio: ffmpeg -loglevel warning -i example/audio.wav -ac 1 -ar 16000 -acodec pcm_s16le -y  ./temp\1004_format.wav]
[2025-08-04 22:27:21,882] [run.py[line:189]] [INFO] [[1004] -> Ԥ�����ʱ:0.1741960048675537s]
[2025-08-04 22:27:21,883] [run.py[line:189]] [INFO] [[1004] Checking for face data cache at: face_cache\1004_format.face_data.pt]
[2025-08-04 22:27:22,162] [run.py[line:189]] [WARNING] [[1004] Failed to load cache file 'face_cache\1004_format.face_data.pt': PytorchStreamReader failed locating file data.pkl: file not found. Re-computing.]
[2025-08-04 22:27:22,162] [run.py[line:189]] [INFO] [[1004] No valid cache found. Starting full video analysis.]
[2025-08-04 22:27:35,246] [process.py[line:108]] [INFO] [>>> Digital human synthesis process started (Refactored for Batching)]
[2025-08-04 22:27:52,132] [run.py[line:189]] [INFO] [[1004] Face data computed and saved to cache. Time: 29.97s]
[2025-08-04 22:27:52,162] [run.py[line:189]] [INFO] [[1004] -> Video analysis/cache load cost: 30.28s]
[2025-08-04 22:28:05,553] [run.py[line:189]] [INFO] [[1004] -> get_aud_feat1 cost:13.3898446559906s]
[2025-08-04 22:28:08,283] [process.py[line:108]] [INFO] [[1004] Cached video producer started. Total frames: 250, Mode: Looping]
[2025-08-04 22:28:08,284] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 0-4/208]
[2025-08-04 22:28:08,312] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 4-8/208]
[2025-08-04 22:28:08,312] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 8-12/208]
[2025-08-04 22:28:08,313] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 12-16/208]
[2025-08-04 22:28:08,314] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 16-20/208]
[2025-08-04 22:28:08,314] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 20-24/208]
[2025-08-04 22:28:08,314] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 24-28/208]
[2025-08-04 22:28:08,315] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 28-32/208]
[2025-08-04 22:28:08,315] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 32-36/208]
[2025-08-04 22:28:08,315] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 36-40/208]
[2025-08-04 22:28:08,329] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 4]
[2025-08-04 22:28:08,348] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 40-44/208]
[2025-08-04 22:28:12,483] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:4, cost:4.15s, batch_size:4]
[2025-08-04 22:28:12,527] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 8]
[2025-08-04 22:28:12,548] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 44-48/208]
[2025-08-04 22:28:12,730] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:8, cost:0.20s, batch_size:4]
[2025-08-04 22:28:12,773] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 12]
[2025-08-04 22:28:12,792] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 48-52/208]
[2025-08-04 22:28:12,979] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:12, cost:0.21s, batch_size:4]
[2025-08-04 22:28:13,037] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 16]
[2025-08-04 22:28:13,075] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 52-56/208]
[2025-08-04 22:28:13,247] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:16, cost:0.21s, batch_size:4]
[2025-08-04 22:28:13,290] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 20]
[2025-08-04 22:28:13,310] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 56-60/208]
[2025-08-04 22:28:13,493] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:20, cost:0.20s, batch_size:4]
[2025-08-04 22:28:13,536] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 24]
[2025-08-04 22:28:13,559] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 60-64/208]
[2025-08-04 22:28:13,737] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:24, cost:0.20s, batch_size:4]
[2025-08-04 22:28:13,781] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 28]
[2025-08-04 22:28:13,800] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 64-68/208]
[2025-08-04 22:28:13,983] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:28, cost:0.20s, batch_size:4]
[2025-08-04 22:28:14,047] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 32]
[2025-08-04 22:28:14,076] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 68-72/208]
[2025-08-04 22:28:14,264] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:32, cost:0.22s, batch_size:4]
[2025-08-04 22:28:14,318] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 36]
[2025-08-04 22:28:14,339] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 72-76/208]
[2025-08-04 22:28:14,519] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:36, cost:0.20s, batch_size:4]
[2025-08-04 22:28:14,564] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 40]
[2025-08-04 22:28:14,582] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 76-80/208]
[2025-08-04 22:28:14,767] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:40, cost:0.20s, batch_size:4]
[2025-08-04 22:28:14,811] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 44]
[2025-08-04 22:28:14,829] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 80-84/208]
[2025-08-04 22:28:15,023] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:44, cost:0.21s, batch_size:4]
[2025-08-04 22:28:15,111] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 48]
[2025-08-04 22:28:15,144] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 84-88/208]
[2025-08-04 22:28:15,319] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:48, cost:0.21s, batch_size:4]
[2025-08-04 22:28:15,362] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 52]
[2025-08-04 22:28:15,384] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 88-92/208]
[2025-08-04 22:28:15,566] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:52, cost:0.20s, batch_size:4]
[2025-08-04 22:28:15,608] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 56]
[2025-08-04 22:28:15,628] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 92-96/208]
[2025-08-04 22:28:15,809] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:56, cost:0.20s, batch_size:4]
[2025-08-04 22:28:15,854] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 60]
[2025-08-04 22:28:15,871] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 96-100/208]
[2025-08-04 22:28:16,069] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:60, cost:0.22s, batch_size:4]
[2025-08-04 22:28:16,159] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 64]
[2025-08-04 22:28:16,194] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 100-104/208]
[2025-08-04 22:28:16,370] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:64, cost:0.21s, batch_size:4]
[2025-08-04 22:28:16,415] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 68]
[2025-08-04 22:28:16,435] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 104-108/208]
[2025-08-04 22:28:16,617] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:68, cost:0.20s, batch_size:4]
[2025-08-04 22:28:16,659] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 72]
[2025-08-04 22:28:16,680] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 108-112/208]
[2025-08-04 22:28:16,863] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:72, cost:0.20s, batch_size:4]
[2025-08-04 22:28:16,907] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 76]
[2025-08-04 22:28:16,927] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 112-116/208]
[2025-08-04 22:28:17,120] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:76, cost:0.21s, batch_size:4]
[2025-08-04 22:28:17,165] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 80]
[2025-08-04 22:28:17,184] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 116-120/208]
[2025-08-04 22:28:17,365] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:80, cost:0.20s, batch_size:4]
[2025-08-04 22:28:17,409] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 84]
[2025-08-04 22:28:17,430] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 120-124/208]
[2025-08-04 22:28:17,610] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:84, cost:0.20s, batch_size:4]
[2025-08-04 22:28:17,654] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 88]
[2025-08-04 22:28:17,674] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 124-128/208]
[2025-08-04 22:28:17,855] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:88, cost:0.20s, batch_size:4]
[2025-08-04 22:28:17,917] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 128-132/208]
[2025-08-04 22:28:17,898] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 92]
[2025-08-04 22:28:18,148] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:92, cost:0.25s, batch_size:4]
[2025-08-04 22:28:18,197] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 96]
[2025-08-04 22:28:18,220] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 132-136/208]
[2025-08-04 22:28:18,400] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:96, cost:0.20s, batch_size:4]
[2025-08-04 22:28:18,444] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 100]
[2025-08-04 22:28:18,465] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 136-140/208]
[2025-08-04 22:28:18,646] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:100, cost:0.20s, batch_size:4]
[2025-08-04 22:28:18,690] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 104]
[2025-08-04 22:28:18,710] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 140-144/208]
[2025-08-04 22:28:18,891] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:104, cost:0.20s, batch_size:4]
[2025-08-04 22:28:18,938] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 108]
[2025-08-04 22:28:18,964] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 144-148/208]
[2025-08-04 22:28:19,159] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:108, cost:0.22s, batch_size:4]
[2025-08-04 22:28:19,212] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 112]
[2025-08-04 22:28:19,236] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 148-152/208]
[2025-08-04 22:28:19,423] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:112, cost:0.21s, batch_size:4]
[2025-08-04 22:28:19,466] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 116]
[2025-08-04 22:28:19,487] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 152-156/208]
[2025-08-04 22:28:19,669] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:116, cost:0.20s, batch_size:4]
[2025-08-04 22:28:19,712] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 120]
[2025-08-04 22:28:19,732] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 156-160/208]
[2025-08-04 22:28:19,916] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:120, cost:0.20s, batch_size:4]
[2025-08-04 22:28:19,958] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 124]
[2025-08-04 22:28:19,978] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 160-164/208]
[2025-08-04 22:28:20,173] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:124, cost:0.21s, batch_size:4]
[2025-08-04 22:28:20,237] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 128]
[2025-08-04 22:28:20,261] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 164-168/208]
[2025-08-04 22:28:20,476] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:128, cost:0.24s, batch_size:4]
[2025-08-04 22:28:20,519] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 132]
[2025-08-04 22:28:20,540] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 168-172/208]
[2025-08-04 22:28:20,723] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:132, cost:0.20s, batch_size:4]
[2025-08-04 22:28:20,765] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 136]
[2025-08-04 22:28:20,786] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 172-176/208]
[2025-08-04 22:28:20,969] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:136, cost:0.20s, batch_size:4]
[2025-08-04 22:28:21,021] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 140]
[2025-08-04 22:28:21,049] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 176-180/208]
[2025-08-04 22:28:21,230] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:140, cost:0.21s, batch_size:4]
[2025-08-04 22:28:21,281] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 144]
[2025-08-04 22:28:21,307] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 180-184/208]
[2025-08-04 22:28:21,485] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:144, cost:0.20s, batch_size:4]
[2025-08-04 22:28:21,527] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 148]
[2025-08-04 22:28:21,547] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 184-188/208]
[2025-08-04 22:28:21,729] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:148, cost:0.20s, batch_size:4]
[2025-08-04 22:28:21,772] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 152]
[2025-08-04 22:28:21,792] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 188-192/208]
[2025-08-04 22:28:21,976] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:152, cost:0.20s, batch_size:4]
[2025-08-04 22:28:22,034] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 156]
[2025-08-04 22:28:22,062] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 192-196/208]
[2025-08-04 22:28:22,239] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:156, cost:0.21s, batch_size:4]
[2025-08-04 22:28:22,290] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 160]
[2025-08-04 22:28:22,319] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 196-200/208]
[2025-08-04 22:28:22,496] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:160, cost:0.21s, batch_size:4]
[2025-08-04 22:28:22,540] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 164]
[2025-08-04 22:28:22,561] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 200-204/208]
[2025-08-04 22:28:22,742] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:164, cost:0.20s, batch_size:4]
[2025-08-04 22:28:22,786] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 168]
[2025-08-04 22:28:22,805] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 204-208/208]
[2025-08-04 22:28:22,806] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> All batches sent.]
[2025-08-04 22:28:22,994] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:168, cost:0.21s, batch_size:4]
[2025-08-04 22:28:23,073] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 172]
[2025-08-04 22:28:23,100] [process.py[line:108]] [INFO] [[1004] task producer process finished.]
[2025-08-04 22:28:23,278] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:172, cost:0.21s, batch_size:4]
[2025-08-04 22:28:23,332] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 176]
[2025-08-04 22:28:23,534] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:176, cost:0.20s, batch_size:4]
[2025-08-04 22:28:23,579] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 180]
[2025-08-04 22:28:23,780] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:180, cost:0.20s, batch_size:4]
[2025-08-04 22:28:23,823] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 184]
[2025-08-04 22:28:24,027] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:184, cost:0.20s, batch_size:4]
[2025-08-04 22:28:24,117] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 188]
[2025-08-04 22:28:24,327] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:188, cost:0.21s, batch_size:4]
[2025-08-04 22:28:24,371] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 192]
[2025-08-04 22:28:24,574] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:192, cost:0.20s, batch_size:4]
[2025-08-04 22:28:24,617] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 196]
[2025-08-04 22:28:24,819] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:196, cost:0.20s, batch_size:4]
[2025-08-04 22:28:24,862] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 200]
[2025-08-04 22:28:25,075] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:200, cost:0.21s, batch_size:4]
[2025-08-04 22:28:25,147] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 204]
[2025-08-04 22:28:25,351] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:204, cost:0.20s, batch_size:4]
[2025-08-04 22:28:25,395] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 208]
[2025-08-04 22:28:25,597] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:208, cost:0.20s, batch_size:4]
[2025-08-04 22:28:25,626] [process.py[line:108]] [INFO] [[1004] Digital human synthesis complete.]
[2025-08-04 22:28:25,668] [run.py[line:72]] [INFO] [Custom VideoWriter [1004]��Ƶ֡���д����ѽ���]
[2025-08-04 22:28:25,668] [run.py[line:75]] [INFO] [Custom VideoWriter Silence Video saved in H:\code\HeyGem\temp\1004-t.mp4]
[2025-08-04 22:28:25,671] [run.py[line:147]] [INFO] [Custom command:ffmpeg -loglevel warning -y -i ./temp\1004_format.wav -i ./temp\1004-t.mp4 -c:a aac -c:v libx264 -crf 15 -strict -2 ./result\1004-r.mp4]
[2025-08-04 22:28:46,171] [sweep_bot.py[line:28]] [INFO] [ɨ�ػ���������Ŀ¼:[./temp]]
[2025-08-04 22:28:46,176] [run.py[line:189]] [INFO] [>>> ����:1004 ��ʱ:84.46756100654602 ]
[2025-08-04 22:29:06,235] [run.py[line:185]] [INFO] [TransDhTask init (with Caching and Batching Optimization)]
[2025-08-04 22:29:16,758] [run.py[line:189]] [INFO] [����:1004 -> audio_url:example/audio.wav  video_url:example/video.mp4]
[2025-08-04 22:29:16,787] [run.py[line:189]] [INFO] [[1004] -> ffmpeg video: ffmpeg -loglevel warning -i example/video.mp4 -crf 15 -vcodec copy -an -y ./temp\1004_format.mp4]
[2025-08-04 22:29:16,858] [run.py[line:189]] [INFO] [[1004] -> ffmpeg audio: ffmpeg -loglevel warning -i example/audio.wav -ac 1 -ar 16000 -acodec pcm_s16le -y  ./temp\1004_format.wav]
[2025-08-04 22:29:16,910] [run.py[line:189]] [INFO] [[1004] -> Ԥ�����ʱ:0.15116381645202637s]
[2025-08-04 22:29:16,910] [run.py[line:189]] [INFO] [[1004] Checking for face data cache at: face_cache\1004_format.face_data.pt]
[2025-08-04 22:29:19,942] [run.py[line:189]] [INFO] [[1004] Successfully loaded cached face data.]
[2025-08-04 22:29:20,840] [run.py[line:189]] [INFO] [[1004] -> Video analysis/cache load cost: 3.93s]
[2025-08-04 22:29:24,147] [run.py[line:189]] [INFO] [[1004] -> get_aud_feat1 cost:3.3060147762298584s]
[2025-08-04 22:29:24,782] [process.py[line:108]] [INFO] [>>> Digital human synthesis process started (Refactored for Batching)]
[2025-08-04 22:29:27,551] [process.py[line:108]] [INFO] [[1004] Cached video producer started. Total frames: 250, Mode: Looping]
[2025-08-04 22:29:27,554] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 0-4/208]
[2025-08-04 22:29:27,650] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 4-8/208]
[2025-08-04 22:29:27,651] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 8-12/208]
[2025-08-04 22:29:27,652] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 12-16/208]
[2025-08-04 22:29:27,652] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 16-20/208]
[2025-08-04 22:29:27,652] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 20-24/208]
[2025-08-04 22:29:27,653] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 24-28/208]
[2025-08-04 22:29:27,653] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 28-32/208]
[2025-08-04 22:29:27,653] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 32-36/208]
[2025-08-04 22:29:27,653] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 36-40/208]
[2025-08-04 22:29:27,684] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 4]
[2025-08-04 22:29:27,734] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 40-44/208]
[2025-08-04 22:29:31,746] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:4, cost:4.06s, batch_size:4]
[2025-08-04 22:29:31,806] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 8]
[2025-08-04 22:29:31,855] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 44-48/208]
[2025-08-04 22:29:32,013] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:8, cost:0.21s, batch_size:4]
[2025-08-04 22:29:32,096] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 12]
[2025-08-04 22:29:32,144] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 48-52/208]
[2025-08-04 22:29:32,349] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:12, cost:0.25s, batch_size:4]
[2025-08-04 22:29:32,406] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 16]
[2025-08-04 22:29:32,451] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 52-56/208]
[2025-08-04 22:29:32,609] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:16, cost:0.20s, batch_size:4]
[2025-08-04 22:29:32,672] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 20]
[2025-08-04 22:29:32,752] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 56-60/208]
[2025-08-04 22:29:32,890] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:20, cost:0.22s, batch_size:4]
[2025-08-04 22:29:32,967] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 24]
[2025-08-04 22:29:33,037] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 60-64/208]
[2025-08-04 22:29:33,174] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:24, cost:0.21s, batch_size:4]
[2025-08-04 22:29:33,231] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 28]
[2025-08-04 22:29:33,284] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 64-68/208]
[2025-08-04 22:29:33,458] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:28, cost:0.23s, batch_size:4]
[2025-08-04 22:29:33,515] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 32]
[2025-08-04 22:29:33,563] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 68-72/208]
[2025-08-04 22:29:33,716] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:32, cost:0.20s, batch_size:4]
[2025-08-04 22:29:33,786] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 36]
[2025-08-04 22:29:33,862] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 72-76/208]
[2025-08-04 22:29:34,004] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:36, cost:0.22s, batch_size:4]
[2025-08-04 22:29:34,069] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 40]
[2025-08-04 22:29:34,122] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 76-80/208]
[2025-08-04 22:29:34,272] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:40, cost:0.20s, batch_size:4]
[2025-08-04 22:29:34,333] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 44]
[2025-08-04 22:29:34,376] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 80-84/208]
[2025-08-04 22:29:34,534] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:44, cost:0.20s, batch_size:4]
[2025-08-04 22:29:34,606] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 48]
[2025-08-04 22:29:34,685] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 84-88/208]
[2025-08-04 22:29:34,818] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:48, cost:0.21s, batch_size:4]
[2025-08-04 22:29:34,874] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 52]
[2025-08-04 22:29:34,924] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 88-92/208]
[2025-08-04 22:29:35,076] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:52, cost:0.20s, batch_size:4]
[2025-08-04 22:29:35,134] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 56]
[2025-08-04 22:29:35,182] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 92-96/208]
[2025-08-04 22:29:35,337] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:56, cost:0.20s, batch_size:4]
[2025-08-04 22:29:35,397] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 60]
[2025-08-04 22:29:35,449] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 96-100/208]
[2025-08-04 22:29:35,600] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:60, cost:0.20s, batch_size:4]
[2025-08-04 22:29:35,668] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 64]
[2025-08-04 22:29:35,747] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 100-104/208]
[2025-08-04 22:29:35,878] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:64, cost:0.21s, batch_size:4]
[2025-08-04 22:29:35,944] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 68]
[2025-08-04 22:29:36,000] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 104-108/208]
[2025-08-04 22:29:36,151] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:68, cost:0.21s, batch_size:4]
[2025-08-04 22:29:36,209] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 72]
[2025-08-04 22:29:36,256] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 108-112/208]
[2025-08-04 22:29:36,413] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:72, cost:0.20s, batch_size:4]
[2025-08-04 22:29:36,471] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 76]
[2025-08-04 22:29:36,516] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 112-116/208]
[2025-08-04 22:29:36,680] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:76, cost:0.21s, batch_size:4]
[2025-08-04 22:29:36,748] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 80]
[2025-08-04 22:29:36,822] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 116-120/208]
[2025-08-04 22:29:36,957] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:80, cost:0.21s, batch_size:4]
[2025-08-04 22:29:37,048] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 84]
[2025-08-04 22:29:37,109] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 120-124/208]
[2025-08-04 22:29:37,257] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:84, cost:0.21s, batch_size:4]
[2025-08-04 22:29:37,316] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 88]
[2025-08-04 22:29:37,367] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 124-128/208]
[2025-08-04 22:29:37,521] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:88, cost:0.21s, batch_size:4]
[2025-08-04 22:29:37,583] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 92]
[2025-08-04 22:29:37,631] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 128-132/208]
[2025-08-04 22:29:37,798] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:92, cost:0.22s, batch_size:4]
[2025-08-04 22:29:37,881] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 96]
[2025-08-04 22:29:37,971] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 132-136/208]
[2025-08-04 22:29:38,091] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:96, cost:0.21s, batch_size:4]
[2025-08-04 22:29:38,149] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 100]
[2025-08-04 22:29:38,194] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 136-140/208]
[2025-08-04 22:29:38,350] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:100, cost:0.20s, batch_size:4]
[2025-08-04 22:29:38,408] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 104]
[2025-08-04 22:29:38,458] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 140-144/208]
[2025-08-04 22:29:38,609] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:104, cost:0.20s, batch_size:4]
[2025-08-04 22:29:38,683] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 108]
[2025-08-04 22:29:38,763] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 144-148/208]
[2025-08-04 22:29:38,893] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:108, cost:0.21s, batch_size:4]
[2025-08-04 22:29:38,959] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 112]
[2025-08-04 22:29:39,025] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 148-152/208]
[2025-08-04 22:29:39,169] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:112, cost:0.21s, batch_size:4]
[2025-08-04 22:29:39,226] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 116]
[2025-08-04 22:29:39,273] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 152-156/208]
[2025-08-04 22:29:39,432] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:116, cost:0.21s, batch_size:4]
[2025-08-04 22:29:39,499] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 120]
[2025-08-04 22:29:39,548] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 156-160/208]
[2025-08-04 22:29:39,706] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:120, cost:0.21s, batch_size:4]
[2025-08-04 22:29:39,779] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 124]
[2025-08-04 22:29:39,847] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 160-164/208]
[2025-08-04 22:29:39,997] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:124, cost:0.22s, batch_size:4]
[2025-08-04 22:29:40,068] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 128]
[2025-08-04 22:29:40,120] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 164-168/208]
[2025-08-04 22:29:40,269] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:128, cost:0.20s, batch_size:4]
[2025-08-04 22:29:40,334] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 132]
[2025-08-04 22:29:40,382] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 168-172/208]
[2025-08-04 22:29:40,536] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:132, cost:0.20s, batch_size:4]
[2025-08-04 22:29:40,608] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 136]
[2025-08-04 22:29:40,679] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 172-176/208]
[2025-08-04 22:29:40,817] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:136, cost:0.21s, batch_size:4]
[2025-08-04 22:29:40,875] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 140]
[2025-08-04 22:29:40,921] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 176-180/208]
[2025-08-04 22:29:41,077] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:140, cost:0.20s, batch_size:4]
[2025-08-04 22:29:41,134] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 144]
[2025-08-04 22:29:41,180] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 180-184/208]
[2025-08-04 22:29:41,335] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:144, cost:0.20s, batch_size:4]
[2025-08-04 22:29:41,392] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 148]
[2025-08-04 22:29:41,438] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 184-188/208]
[2025-08-04 22:29:41,594] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:148, cost:0.20s, batch_size:4]
[2025-08-04 22:29:41,666] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 152]
[2025-08-04 22:29:41,739] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 188-192/208]
[2025-08-04 22:29:41,877] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:152, cost:0.21s, batch_size:4]
[2025-08-04 22:29:41,935] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 156]
[2025-08-04 22:29:41,987] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 192-196/208]
[2025-08-04 22:29:42,140] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:156, cost:0.21s, batch_size:4]
[2025-08-04 22:29:42,197] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 160]
[2025-08-04 22:29:42,242] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 196-200/208]
[2025-08-04 22:29:42,407] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:160, cost:0.21s, batch_size:4]
[2025-08-04 22:29:42,466] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 164]
[2025-08-04 22:29:42,515] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 200-204/208]
[2025-08-04 22:29:42,668] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:164, cost:0.20s, batch_size:4]
[2025-08-04 22:29:42,737] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 168]
[2025-08-04 22:29:42,823] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 204-208/208]
[2025-08-04 22:29:42,824] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> All batches sent.]
[2025-08-04 22:29:42,947] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:168, cost:0.21s, batch_size:4]
[2025-08-04 22:29:43,017] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 172]
[2025-08-04 22:29:43,096] [process.py[line:108]] [INFO] [[1004] task producer process finished.]
[2025-08-04 22:29:43,226] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:172, cost:0.21s, batch_size:4]
[2025-08-04 22:29:43,282] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 176]
[2025-08-04 22:29:43,483] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:176, cost:0.20s, batch_size:4]
[2025-08-04 22:29:43,540] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 180]
[2025-08-04 22:29:43,742] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:180, cost:0.20s, batch_size:4]
[2025-08-04 22:29:43,881] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 184]
[2025-08-04 22:29:44,087] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:184, cost:0.21s, batch_size:4]
[2025-08-04 22:29:44,142] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 188]
[2025-08-04 22:29:44,342] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:188, cost:0.20s, batch_size:4]
[2025-08-04 22:29:44,400] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 192]
[2025-08-04 22:29:44,602] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:192, cost:0.20s, batch_size:4]
[2025-08-04 22:29:44,673] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 196]
[2025-08-04 22:29:44,883] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:196, cost:0.21s, batch_size:4]
[2025-08-04 22:29:44,942] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 200]
[2025-08-04 22:29:45,146] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:200, cost:0.20s, batch_size:4]
[2025-08-04 22:29:45,203] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 204]
[2025-08-04 22:29:45,414] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:204, cost:0.21s, batch_size:4]
[2025-08-04 22:29:45,470] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 208]
[2025-08-04 22:29:45,671] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:208, cost:0.20s, batch_size:4]
[2025-08-04 22:29:45,699] [process.py[line:108]] [INFO] [[1004] Digital human synthesis complete.]
[2025-08-04 22:29:45,740] [run.py[line:72]] [INFO] [Custom VideoWriter [1004]��Ƶ֡���д����ѽ���]
[2025-08-04 22:29:45,740] [run.py[line:75]] [INFO] [Custom VideoWriter Silence Video saved in H:\code\HeyGem\temp\1004-t.mp4]
[2025-08-04 22:29:45,742] [run.py[line:147]] [INFO] [Custom command:ffmpeg -loglevel warning -y -i ./temp\1004_format.wav -i ./temp\1004-t.mp4 -c:a aac -c:v libx264 -crf 15 -strict -2 ./result\1004-r.mp4]
[2025-08-04 22:30:07,992] [sweep_bot.py[line:28]] [INFO] [ɨ�ػ���������Ŀ¼:[./temp]]
[2025-08-04 22:30:07,997] [run.py[line:189]] [INFO] [>>> ����:1004 ��ʱ:51.23739433288574 ]
[2025-08-04 22:31:44,208] [run.py[line:185]] [INFO] [TransDhTask init (with Caching and Batching Optimization)]
[2025-08-04 22:31:54,707] [run.py[line:189]] [INFO] [����:1004 -> audio_url:example/audio.wav  video_url:example/video.mp4]
[2025-08-04 22:31:54,732] [run.py[line:189]] [INFO] [[1004] -> ffmpeg video: ffmpeg -loglevel warning -i example/video.mp4 -crf 15 -vcodec copy -an -y ./temp\1004_format.mp4]
[2025-08-04 22:31:54,790] [run.py[line:189]] [INFO] [[1004] -> ffmpeg audio: ffmpeg -loglevel warning -i example/audio.wav -ac 1 -ar 16000 -acodec pcm_s16le -y  ./temp\1004_format.wav]
[2025-08-04 22:31:54,842] [run.py[line:189]] [INFO] [[1004] -> Ԥ�����ʱ:0.13409852981567383s]
[2025-08-04 22:31:54,842] [run.py[line:189]] [INFO] [[1004] Checking for face data cache at: face_cache\1004_format.face_data.pt]
[2025-08-04 22:31:57,706] [run.py[line:189]] [INFO] [[1004] Successfully loaded cached face data.]
[2025-08-04 22:31:58,603] [run.py[line:189]] [INFO] [[1004] -> Video analysis/cache load cost: 3.76s]
[2025-08-04 22:32:01,617] [run.py[line:189]] [INFO] [[1004] -> get_aud_feat1 cost:3.01332426071167s]
[2025-08-04 22:32:02,621] [process.py[line:108]] [INFO] [>>> Digital human synthesis process started (Refactored for Batching)]
[2025-08-04 22:32:04,961] [process.py[line:108]] [INFO] [[1004] Cached video producer started. Total frames: 250, Mode: Looping]
[2025-08-04 22:32:04,963] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 0-4/208]
[2025-08-04 22:32:05,056] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 4-8/208]
[2025-08-04 22:32:05,057] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 8-12/208]
[2025-08-04 22:32:05,058] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 12-16/208]
[2025-08-04 22:32:05,058] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 16-20/208]
[2025-08-04 22:32:05,059] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 20-24/208]
[2025-08-04 22:32:05,059] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 24-28/208]
[2025-08-04 22:32:05,059] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 28-32/208]
[2025-08-04 22:32:05,059] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 32-36/208]
[2025-08-04 22:32:05,059] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 36-40/208]
[2025-08-04 22:32:05,088] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 4]
[2025-08-04 22:32:05,137] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 40-44/208]
[2025-08-04 22:32:09,130] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:4, cost:4.04s, batch_size:4]
[2025-08-04 22:32:09,191] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 8]
[2025-08-04 22:32:09,244] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 44-48/208]
[2025-08-04 22:32:09,392] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:8, cost:0.20s, batch_size:4]
[2025-08-04 22:32:09,453] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 12]
[2025-08-04 22:32:09,507] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 48-52/208]
[2025-08-04 22:32:09,656] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:12, cost:0.20s, batch_size:4]
[2025-08-04 22:32:09,769] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 16]
[2025-08-04 22:32:09,842] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 52-56/208]
[2025-08-04 22:32:09,977] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:16, cost:0.21s, batch_size:4]
[2025-08-04 22:32:10,057] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 20]
[2025-08-04 22:32:10,104] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 56-60/208]
[2025-08-04 22:32:10,262] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:20, cost:0.21s, batch_size:4]
[2025-08-04 22:32:10,323] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 24]
[2025-08-04 22:32:10,368] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 60-64/208]
[2025-08-04 22:32:10,525] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:24, cost:0.20s, batch_size:4]
[2025-08-04 22:32:10,608] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 28]
[2025-08-04 22:32:10,686] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 64-68/208]
[2025-08-04 22:32:10,820] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:28, cost:0.21s, batch_size:4]
[2025-08-04 22:32:10,878] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 32]
[2025-08-04 22:32:10,926] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 68-72/208]
[2025-08-04 22:32:11,082] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:32, cost:0.20s, batch_size:4]
[2025-08-04 22:32:11,139] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 36]
[2025-08-04 22:32:11,187] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 72-76/208]
[2025-08-04 22:32:11,342] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:36, cost:0.20s, batch_size:4]
[2025-08-04 22:32:11,400] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 40]
[2025-08-04 22:32:11,449] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 76-80/208]
[2025-08-04 22:32:11,610] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:40, cost:0.21s, batch_size:4]
[2025-08-04 22:32:11,676] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 44]
[2025-08-04 22:32:11,724] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 80-84/208]
[2025-08-04 22:32:11,879] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:44, cost:0.20s, batch_size:4]
[2025-08-04 22:32:11,937] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 48]
[2025-08-04 22:32:11,982] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 84-88/208]
[2025-08-04 22:32:12,142] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:48, cost:0.20s, batch_size:4]
[2025-08-04 22:32:12,208] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 52]
[2025-08-04 22:32:12,253] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 88-92/208]
[2025-08-04 22:32:12,437] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:52, cost:0.23s, batch_size:4]
[2025-08-04 22:32:12,502] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 56]
[2025-08-04 22:32:12,561] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 92-96/208]
[2025-08-04 22:32:12,725] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:56, cost:0.22s, batch_size:4]
[2025-08-04 22:32:12,782] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 60]
[2025-08-04 22:32:12,829] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 96-100/208]
[2025-08-04 22:32:12,983] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:60, cost:0.20s, batch_size:4]
[2025-08-04 22:32:13,073] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 64]
[2025-08-04 22:32:13,134] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 100-104/208]
[2025-08-04 22:32:13,287] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:64, cost:0.22s, batch_size:4]
[2025-08-04 22:32:13,345] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 68]
[2025-08-04 22:32:13,396] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 104-108/208]
[2025-08-04 22:32:13,549] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:68, cost:0.20s, batch_size:4]
[2025-08-04 22:32:13,610] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 72]
[2025-08-04 22:32:13,678] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 108-112/208]
[2025-08-04 22:32:13,818] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:72, cost:0.21s, batch_size:4]
[2025-08-04 22:32:13,874] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 76]
[2025-08-04 22:32:13,923] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 112-116/208]
[2025-08-04 22:32:14,077] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:76, cost:0.20s, batch_size:4]
[2025-08-04 22:32:14,136] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 80]
[2025-08-04 22:32:14,183] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 116-120/208]
[2025-08-04 22:32:14,339] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:80, cost:0.20s, batch_size:4]
[2025-08-04 22:32:14,398] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 84]
[2025-08-04 22:32:14,445] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 120-124/208]
[2025-08-04 22:32:14,611] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:84, cost:0.21s, batch_size:4]
[2025-08-04 22:32:14,700] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 88]
[2025-08-04 22:32:14,746] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 124-128/208]
[2025-08-04 22:32:14,904] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:88, cost:0.21s, batch_size:4]
[2025-08-04 22:32:14,964] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 92]
[2025-08-04 22:32:15,023] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 128-132/208]
[2025-08-04 22:32:15,181] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:92, cost:0.22s, batch_size:4]
[2025-08-04 22:32:15,246] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 96]
[2025-08-04 22:32:15,295] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 132-136/208]
[2025-08-04 22:32:15,461] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:96, cost:0.22s, batch_size:4]
[2025-08-04 22:32:15,528] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 100]
[2025-08-04 22:32:15,603] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 136-140/208]
[2025-08-04 22:32:15,748] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:100, cost:0.22s, batch_size:4]
[2025-08-04 22:32:15,828] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 104]
[2025-08-04 22:32:15,880] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 140-144/208]
[2025-08-04 22:32:16,044] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:104, cost:0.22s, batch_size:4]
[2025-08-04 22:32:16,109] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 108]
[2025-08-04 22:32:16,157] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 144-148/208]
[2025-08-04 22:32:16,314] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:108, cost:0.21s, batch_size:4]
[2025-08-04 22:32:16,374] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 112]
[2025-08-04 22:32:16,427] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 148-152/208]
[2025-08-04 22:32:16,591] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:112, cost:0.22s, batch_size:4]
[2025-08-04 22:32:16,728] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 116]
[2025-08-04 22:32:16,809] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 152-156/208]
[2025-08-04 22:32:16,949] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:116, cost:0.22s, batch_size:4]
[2025-08-04 22:32:17,017] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 120]
[2025-08-04 22:32:17,087] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 156-160/208]
[2025-08-04 22:32:17,230] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:120, cost:0.22s, batch_size:4]
[2025-08-04 22:32:17,292] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 124]
[2025-08-04 22:32:17,344] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 160-164/208]
[2025-08-04 22:32:17,496] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:124, cost:0.20s, batch_size:4]
[2025-08-04 22:32:17,562] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 128]
[2025-08-04 22:32:17,636] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 164-168/208]
[2025-08-04 22:32:17,779] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:128, cost:0.22s, batch_size:4]
[2025-08-04 22:32:17,839] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 132]
[2025-08-04 22:32:17,887] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 168-172/208]
[2025-08-04 22:32:18,052] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:132, cost:0.21s, batch_size:4]
[2025-08-04 22:32:18,111] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 136]
[2025-08-04 22:32:18,156] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 172-176/208]
[2025-08-04 22:32:18,317] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:136, cost:0.21s, batch_size:4]
[2025-08-04 22:32:18,375] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 140]
[2025-08-04 22:32:18,421] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 176-180/208]
[2025-08-04 22:32:18,584] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:140, cost:0.21s, batch_size:4]
[2025-08-04 22:32:18,724] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 144]
[2025-08-04 22:32:18,790] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 180-184/208]
[2025-08-04 22:32:18,946] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:144, cost:0.22s, batch_size:4]
[2025-08-04 22:32:19,006] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 148]
[2025-08-04 22:32:19,085] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 184-188/208]
[2025-08-04 22:32:19,218] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:148, cost:0.21s, batch_size:4]
[2025-08-04 22:32:19,276] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 152]
[2025-08-04 22:32:19,327] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 188-192/208]
[2025-08-04 22:32:19,478] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:152, cost:0.20s, batch_size:4]
[2025-08-04 22:32:19,542] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 156]
[2025-08-04 22:32:19,608] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 192-196/208]
[2025-08-04 22:32:19,758] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:156, cost:0.22s, batch_size:4]
[2025-08-04 22:32:19,833] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 160]
[2025-08-04 22:32:19,880] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 196-200/208]
[2025-08-04 22:32:20,036] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:160, cost:0.20s, batch_size:4]
[2025-08-04 22:32:20,113] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 164]
[2025-08-04 22:32:20,162] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 200-204/208]
[2025-08-04 22:32:20,319] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:164, cost:0.21s, batch_size:4]
[2025-08-04 22:32:20,377] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 168]
[2025-08-04 22:32:20,424] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 204-208/208]
[2025-08-04 22:32:20,425] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> All batches sent.]
[2025-08-04 22:32:20,584] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:168, cost:0.21s, batch_size:4]
[2025-08-04 22:32:20,713] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 172]
[2025-08-04 22:32:20,794] [process.py[line:108]] [INFO] [[1004] task producer process finished.]
[2025-08-04 22:32:20,928] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:172, cost:0.22s, batch_size:4]
[2025-08-04 22:32:20,986] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 176]
[2025-08-04 22:32:21,187] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:176, cost:0.20s, batch_size:4]
[2025-08-04 22:32:21,244] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 180]
[2025-08-04 22:32:21,466] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:180, cost:0.22s, batch_size:4]
[2025-08-04 22:32:21,522] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 184]
[2025-08-04 22:32:21,734] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:184, cost:0.21s, batch_size:4]
[2025-08-04 22:32:21,804] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 188]
[2025-08-04 22:32:22,008] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:188, cost:0.20s, batch_size:4]
[2025-08-04 22:32:22,103] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 192]
[2025-08-04 22:32:22,306] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:192, cost:0.20s, batch_size:4]
[2025-08-04 22:32:22,364] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 196]
[2025-08-04 22:32:22,566] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:196, cost:0.20s, batch_size:4]
[2025-08-04 22:32:22,670] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 200]
[2025-08-04 22:32:22,885] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:200, cost:0.22s, batch_size:4]
[2025-08-04 22:32:22,944] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 204]
[2025-08-04 22:32:23,150] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:204, cost:0.21s, batch_size:4]
[2025-08-04 22:32:23,204] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 208]
[2025-08-04 22:32:23,422] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:208, cost:0.22s, batch_size:4]
[2025-08-04 22:32:23,449] [process.py[line:108]] [INFO] [[1004] Digital human synthesis complete.]
[2025-08-04 22:32:23,489] [run.py[line:72]] [INFO] [Custom VideoWriter [1004]��Ƶ֡���д����ѽ���]
[2025-08-04 22:32:23,490] [run.py[line:75]] [INFO] [Custom VideoWriter Silence Video saved in H:\code\HeyGem\temp\1004-t.mp4]
[2025-08-04 22:32:23,491] [run.py[line:147]] [INFO] [Custom command:ffmpeg -loglevel warning -y -i ./temp\1004_format.wav -i ./temp\1004-t.mp4 -c:a aac -c:v libx264 -crf 15 -strict -2 ./result\1004-r.mp4]
[2025-08-04 22:32:45,787] [sweep_bot.py[line:28]] [INFO] [ɨ�ػ���������Ŀ¼:[./temp]]
[2025-08-04 22:32:45,791] [run.py[line:189]] [INFO] [>>> ����:1004 ��ʱ:51.0832884311676 ]
[2025-08-04 22:34:28,864] [run.py[line:185]] [INFO] [TransDhTask init (with Caching and Batching Optimization)]
[2025-08-04 22:34:39,364] [run.py[line:189]] [INFO] [����:1004 -> audio_url:example/audio.wav  video_url:example/video.mp4]
[2025-08-04 22:34:39,389] [run.py[line:189]] [INFO] [[1004] -> ffmpeg video: ffmpeg -loglevel warning -i example/video.mp4 -crf 15 -vcodec copy -an -y ./temp\1004_format.mp4]
[2025-08-04 22:34:39,455] [run.py[line:189]] [INFO] [[1004] -> ffmpeg audio: ffmpeg -loglevel warning -i example/audio.wav -ac 1 -ar 16000 -acodec pcm_s16le -y  ./temp\1004_format.wav]
[2025-08-04 22:34:39,508] [run.py[line:189]] [INFO] [[1004] -> Ԥ�����ʱ:0.14302515983581543s]
[2025-08-04 22:34:39,508] [run.py[line:189]] [INFO] [[1004] Checking for face data cache at: face_cache\1004_format.face_data.pt]
[2025-08-04 22:34:42,495] [run.py[line:189]] [INFO] [[1004] Successfully loaded cached face data.]
[2025-08-04 22:34:43,441] [run.py[line:189]] [INFO] [[1004] -> Video analysis/cache load cost: 3.93s]
[2025-08-04 22:34:46,450] [run.py[line:189]] [INFO] [[1004] -> get_aud_feat1 cost:3.0083060264587402s]
[2025-08-04 22:34:47,566] [process.py[line:108]] [INFO] [>>> Digital human synthesis process started (Refactored for Batching)]
[2025-08-04 22:34:49,872] [process.py[line:108]] [INFO] [[1004] Cached video producer started. Total frames: 250, Mode: Looping]
[2025-08-04 22:34:49,875] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 0-4/208]
[2025-08-04 22:34:49,967] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 4-8/208]
[2025-08-04 22:34:49,968] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 8-12/208]
[2025-08-04 22:34:49,969] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 12-16/208]
[2025-08-04 22:34:49,970] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 16-20/208]
[2025-08-04 22:34:49,970] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 20-24/208]
[2025-08-04 22:34:49,970] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 24-28/208]
[2025-08-04 22:34:49,970] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 28-32/208]
[2025-08-04 22:34:49,971] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 32-36/208]
[2025-08-04 22:34:49,971] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 36-40/208]
[2025-08-04 22:34:49,999] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 4]
[2025-08-04 22:34:50,076] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 40-44/208]
[2025-08-04 22:34:54,000] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:4, cost:4.00s, batch_size:4]
[2025-08-04 22:34:54,059] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 8]
[2025-08-04 22:34:54,114] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 44-48/208]
[2025-08-04 22:34:54,264] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:8, cost:0.21s, batch_size:4]
[2025-08-04 22:34:54,324] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 12]
[2025-08-04 22:34:54,378] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 48-52/208]
[2025-08-04 22:34:54,531] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:12, cost:0.21s, batch_size:4]
[2025-08-04 22:34:54,589] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 16]
[2025-08-04 22:34:54,638] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 52-56/208]
[2025-08-04 22:34:54,795] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:16, cost:0.21s, batch_size:4]
[2025-08-04 22:34:54,854] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 20]
[2025-08-04 22:34:54,911] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 56-60/208]
[2025-08-04 22:34:55,060] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:20, cost:0.21s, batch_size:4]
[2025-08-04 22:34:55,127] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 24]
[2025-08-04 22:34:55,186] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 60-64/208]
[2025-08-04 22:34:55,336] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:24, cost:0.21s, batch_size:4]
[2025-08-04 22:34:55,424] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 28]
[2025-08-04 22:34:55,489] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 64-68/208]
[2025-08-04 22:34:55,634] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:28, cost:0.21s, batch_size:4]
[2025-08-04 22:34:55,717] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 32]
[2025-08-04 22:34:55,772] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 68-72/208]
[2025-08-04 22:34:55,924] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:32, cost:0.21s, batch_size:4]
[2025-08-04 22:34:56,023] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 36]
[2025-08-04 22:34:56,120] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 72-76/208]
[2025-08-04 22:34:56,250] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:36, cost:0.23s, batch_size:4]
[2025-08-04 22:34:56,315] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 40]
[2025-08-04 22:34:56,364] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 76-80/208]
[2025-08-04 22:34:56,516] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:40, cost:0.20s, batch_size:4]
[2025-08-04 22:34:56,580] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 44]
[2025-08-04 22:34:56,669] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 80-84/208]
[2025-08-04 22:34:56,792] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:44, cost:0.21s, batch_size:4]
[2025-08-04 22:34:56,851] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 48]
[2025-08-04 22:34:56,905] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 84-88/208]
[2025-08-04 22:34:57,056] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:48, cost:0.21s, batch_size:4]
[2025-08-04 22:34:57,123] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 52]
[2025-08-04 22:34:57,185] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 88-92/208]
[2025-08-04 22:34:57,333] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:52, cost:0.21s, batch_size:4]
[2025-08-04 22:34:57,414] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 56]
[2025-08-04 22:34:57,490] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 92-96/208]
[2025-08-04 22:34:57,625] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:56, cost:0.21s, batch_size:4]
[2025-08-04 22:34:57,683] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 60]
[2025-08-04 22:34:57,732] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 96-100/208]
[2025-08-04 22:34:57,887] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:60, cost:0.20s, batch_size:4]
[2025-08-04 22:34:57,944] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 64]
[2025-08-04 22:34:58,003] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 100-104/208]
[2025-08-04 22:34:58,159] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:64, cost:0.21s, batch_size:4]
[2025-08-04 22:34:58,230] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 68]
[2025-08-04 22:34:58,287] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 104-108/208]
[2025-08-04 22:34:58,442] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:68, cost:0.21s, batch_size:4]
[2025-08-04 22:34:58,514] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 72]
[2025-08-04 22:34:58,588] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 108-112/208]
[2025-08-04 22:34:58,728] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:72, cost:0.22s, batch_size:4]
[2025-08-04 22:34:58,791] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 76]
[2025-08-04 22:34:58,838] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 112-116/208]
[2025-08-04 22:34:58,995] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:76, cost:0.20s, batch_size:4]
[2025-08-04 22:34:59,095] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 80]
[2025-08-04 22:34:59,153] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 116-120/208]
[2025-08-04 22:34:59,301] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:80, cost:0.21s, batch_size:4]
[2025-08-04 22:34:59,360] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 84]
[2025-08-04 22:34:59,411] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 120-124/208]
[2025-08-04 22:34:59,564] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:84, cost:0.21s, batch_size:4]
[2025-08-04 22:34:59,624] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 88]
[2025-08-04 22:34:59,673] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 124-128/208]
[2025-08-04 22:34:59,831] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:88, cost:0.21s, batch_size:4]
[2025-08-04 22:34:59,891] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 92]
[2025-08-04 22:34:59,971] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 128-132/208]
[2025-08-04 22:35:00,100] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:92, cost:0.21s, batch_size:4]
[2025-08-04 22:35:00,171] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 96]
[2025-08-04 22:35:00,229] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 132-136/208]
[2025-08-04 22:35:00,380] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:96, cost:0.21s, batch_size:4]
[2025-08-04 22:35:00,453] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 100]
[2025-08-04 22:35:00,539] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 136-140/208]
[2025-08-04 22:35:00,676] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:100, cost:0.22s, batch_size:4]
[2025-08-04 22:35:00,736] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 104]
[2025-08-04 22:35:00,782] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 140-144/208]
[2025-08-04 22:35:00,942] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:104, cost:0.21s, batch_size:4]
[2025-08-04 22:35:00,999] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 108]
[2025-08-04 22:35:01,082] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 144-148/208]
[2025-08-04 22:35:01,213] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:108, cost:0.21s, batch_size:4]
[2025-08-04 22:35:01,293] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 112]
[2025-08-04 22:35:01,351] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 148-152/208]
[2025-08-04 22:35:01,500] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:112, cost:0.21s, batch_size:4]
[2025-08-04 22:35:01,582] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 116]
[2025-08-04 22:35:01,663] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 152-156/208]
[2025-08-04 22:35:01,798] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:116, cost:0.22s, batch_size:4]
[2025-08-04 22:35:01,855] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 120]
[2025-08-04 22:35:01,902] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 156-160/208]
[2025-08-04 22:35:02,062] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:120, cost:0.21s, batch_size:4]
[2025-08-04 22:35:02,125] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 124]
[2025-08-04 22:35:02,181] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 160-164/208]
[2025-08-04 22:35:02,328] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:124, cost:0.20s, batch_size:4]
[2025-08-04 22:35:02,400] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 128]
[2025-08-04 22:35:02,464] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 164-168/208]
[2025-08-04 22:35:02,607] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:128, cost:0.21s, batch_size:4]
[2025-08-04 22:35:02,666] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 132]
[2025-08-04 22:35:02,714] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 168-172/208]
[2025-08-04 22:35:02,872] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:132, cost:0.21s, batch_size:4]
[2025-08-04 22:35:02,936] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 136]
[2025-08-04 22:35:03,001] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 172-176/208]
[2025-08-04 22:35:03,151] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:136, cost:0.22s, batch_size:4]
[2025-08-04 22:35:03,223] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 140]
[2025-08-04 22:35:03,286] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 176-180/208]
[2025-08-04 22:35:03,464] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:140, cost:0.24s, batch_size:4]
[2025-08-04 22:35:03,539] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 144]
[2025-08-04 22:35:03,603] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 180-184/208]
[2025-08-04 22:35:03,747] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:144, cost:0.21s, batch_size:4]
[2025-08-04 22:35:03,815] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 148]
[2025-08-04 22:35:03,862] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 184-188/208]
[2025-08-04 22:35:04,029] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:148, cost:0.21s, batch_size:4]
[2025-08-04 22:35:04,103] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 152]
[2025-08-04 22:35:04,149] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 188-192/208]
[2025-08-04 22:35:04,314] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:152, cost:0.21s, batch_size:4]
[2025-08-04 22:35:04,390] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 156]
[2025-08-04 22:35:04,465] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 192-196/208]
[2025-08-04 22:35:04,603] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:156, cost:0.21s, batch_size:4]
[2025-08-04 22:35:04,665] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 160]
[2025-08-04 22:35:04,710] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 196-200/208]
[2025-08-04 22:35:04,871] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:160, cost:0.21s, batch_size:4]
[2025-08-04 22:35:04,930] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 164]
[2025-08-04 22:35:04,987] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 200-204/208]
[2025-08-04 22:35:05,140] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:164, cost:0.21s, batch_size:4]
[2025-08-04 22:35:05,209] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 168]
[2025-08-04 22:35:05,266] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> Sent batch. Frames 204-208/208]
[2025-08-04 22:35:05,268] [process.py[line:108]] [INFO] [drivered_video_from_cache >>> All batches sent.]
[2025-08-04 22:35:05,420] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:168, cost:0.21s, batch_size:4]
[2025-08-04 22:35:05,492] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 172]
[2025-08-04 22:35:05,590] [process.py[line:108]] [INFO] [[1004] task producer process finished.]
[2025-08-04 22:35:05,707] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:172, cost:0.22s, batch_size:4]
[2025-08-04 22:35:05,764] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 176]
[2025-08-04 22:35:05,966] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:176, cost:0.20s, batch_size:4]
[2025-08-04 22:35:06,050] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 180]
[2025-08-04 22:35:06,257] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:180, cost:0.21s, batch_size:4]
[2025-08-04 22:35:06,330] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 184]
[2025-08-04 22:35:06,532] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:184, cost:0.20s, batch_size:4]
[2025-08-04 22:35:06,618] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 188]
[2025-08-04 22:35:06,824] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:188, cost:0.21s, batch_size:4]
[2025-08-04 22:35:06,883] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 192]
[2025-08-04 22:35:07,095] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:192, cost:0.21s, batch_size:4]
[2025-08-04 22:35:07,155] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 196]
[2025-08-04 22:35:07,356] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:196, cost:0.20s, batch_size:4]
[2025-08-04 22:35:07,416] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 200]
[2025-08-04 22:35:07,628] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:200, cost:0.21s, batch_size:4]
[2025-08-04 22:35:07,686] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 204]
[2025-08-04 22:35:07,887] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:204, cost:0.20s, batch_size:4]
[2025-08-04 22:35:07,945] [process.py[line:108]] [INFO] [>>> audio_transfer received batch. Size: 4, Up to frameId: 208]
[2025-08-04 22:35:08,149] [process.py[line:108]] [INFO] [audio_transfer >>> Sent processed batch to writer. frameId:208, cost:0.20s, batch_size:4]
[2025-08-04 22:35:08,176] [process.py[line:108]] [INFO] [[1004] Digital human synthesis complete.]
[2025-08-04 22:35:08,228] [run.py[line:72]] [INFO] [Custom VideoWriter [1004]��Ƶ֡���д����ѽ���]
[2025-08-04 22:35:08,229] [run.py[line:75]] [INFO] [Custom VideoWriter Silence Video saved in H:\code\HeyGem\temp\1004-t.mp4]
[2025-08-04 22:35:08,231] [run.py[line:147]] [INFO] [Custom command:ffmpeg -loglevel warning -y -i ./temp\1004_format.wav -i ./temp\1004-t.mp4 -c:a aac -c:v libx264 -crf 15 -strict -2 ./result\1004-r.mp4]
[2025-08-04 22:35:30,512] [sweep_bot.py[line:28]] [INFO] [ɨ�ػ���������Ŀ¼:[./temp]]
[2025-08-04 22:35:30,516] [run.py[line:189]] [INFO] [>>> ����:1004 ��ʱ:51.15086507797241 ]
